import express, { Router } from "express";
import contactRoute from "./contact.routes";
import analyticsRoute from "./analytics.routes";
import recipeRoute from "./recipe.routes";

const routes: Router = express.Router();

// Contact Us routes (public - no authentication required)
routes.use("/contact-us", contactRoute);

// Analytics routes (public - no authentication required)
routes.use("/analytics", analyticsRoute);
// Recipe routes (public - no authentication required)
routes.use("/recipes", recipeRoute);

export default routes;
