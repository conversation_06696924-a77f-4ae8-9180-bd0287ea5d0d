import fs from "fs";
import path from "path";
import crypto from "crypto";
import {
  S3Client,
  PutObjectCommand,
  HeadObjectCommand,
} from "@aws-sdk/client-s3";
import { RECIPE_FILE_UPLOAD_CONSTANT } from "../helper/common";
import { Item, item_status, item_type, item_external_location, item_IEC } from "../models/Item";

// Setup S3 client
const s3 = new S3Client({
  endpoint: (global as any).config.MINIO_ENDPOINT,
  region: "us-east-1",
  forcePathStyle: true,
  credentials: {
    accessKeyId: (global as any).config.MINIO_ACCESS_KEY,
    secretAccessKey: (global as any).config.MINIO_SECRET_KEY,
  },
});

/**
 * Upload sample files to S3 bucket and create database entries
 */
export class SampleFileUploadService {
  private bucketName: string;

  constructor() {
    this.bucketName = (global as any).config.MINIO_BUCKET_NAME || "recipe-management";
  }

  /**
   * Upload ingredient import sample file to S3
   */
  async uploadIngredientSampleFile(): Promise<{
    success: boolean;
    fileUrl?: string;
    itemId?: number;
    error?: any;
  }> {
    try {
      const sampleFilePath = path.join(process.cwd(), "sample_files", "MicrOffice_Ingredient_Sample.xlsx");

      // Check if file exists
      if (!fs.existsSync(sampleFilePath)) {
        throw new Error(`Sample file not found at: ${sampleFilePath}`);
      }

      // Read file
      const fileBuffer = fs.readFileSync(sampleFilePath);
      const fileName = "ingredient_import_template.xlsx";
      const mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

      // Generate file hash
      const fileHash = crypto
        .createHash("md5")
        .update(fileBuffer)
        .digest("hex");

      // Check if file already exists in database
      const existingItem = await Item.findOne({
        where: {
          item_hash: fileHash,
          item_organization_id: null as any, // System file
        },
      });

      if (existingItem) {
        // File already exists, return existing URL
        const fileUrl = this.generateFileUrl(existingItem.item_location);
        return {
          success: true,
          fileUrl,
          itemId: existingItem.id,
        };
      }

      // Generate S3 path using the constant
      const s3Path = RECIPE_FILE_UPLOAD_CONSTANT.SAMPLE_FILES.destinationPath(
        null, // No organization (system file)
        "ingredients",
        fileName
      );

      // Check if file exists in S3
      let fileExistsInS3 = false;
      try {
        await s3.send(new HeadObjectCommand({
          Bucket: this.bucketName,
          Key: s3Path,
        }));
        fileExistsInS3 = true;
      } catch {
        // File doesn't exist in S3, will upload
      }

      // Upload to S3 if not exists
      if (!fileExistsInS3) {
        await s3.send(
          new PutObjectCommand({
            Bucket: this.bucketName,
            Key: s3Path,
            Body: fileBuffer,
            ContentType: mimeType,
            Metadata: {
              purpose: "ingredient_import_template",
              uploadedBy: "system",
              uploadedAt: new Date().toISOString(),
            },
          })
        );
      }

      // Create database entry
      const itemData: any = {
        item_type: item_type.PDF, // Excel files are treated as documents
        item_name: fileName,
        item_hash: fileHash,
        item_mime_type: mimeType,
        item_extension: ".xlsx",
        item_size: fileBuffer.length,
        item_IEC: item_IEC.B,
        item_status: item_status.ACTIVE,
        item_external_location: item_external_location.NO,
        item_location: s3Path,
        item_organization_id: null, // System file
        item_category: "sample_template",
        created_by: 1, // System user
        updated_by: 1, // System user
      };

      const newItem = await Item.create(itemData);
      const fileUrl = this.generateFileUrl(s3Path);

      return {
        success: true,
        fileUrl,
        itemId: newItem.id,
      };

    } catch (error) {
      console.error("Error uploading sample file:", error);
      return {
        success: false,
        error: error,
      };
    }
  }

  /**
   * Generate file URL for accessing the file
   */
  private generateFileUrl(itemLocation: string): string {
    const baseUrl = (global as any).config?.API_BASE_URL;

    if (baseUrl && baseUrl.includes("/backend-api/v1/public/user/get-file?location=")) {
      return `${baseUrl}${itemLocation}`;
    } else {
      const fallbackUrl = "https://staging.namastevillage.theeasyaccess.com";
      return `${fallbackUrl}/backend-api/v1/public/user/get-file?location=${itemLocation}`;
    }
  }

  /**
   * Upload multiple sample files for different purposes
   */
  async uploadAllSampleFiles(): Promise<{
    success: boolean;
    results: any[];
    error?: any;
  }> {
    try {
      const results = [];

      // Upload ingredient sample file
      const ingredientResult = await this.uploadIngredientSampleFile();
      results.push({
        type: "ingredient_import_template",
        ...ingredientResult,
      });

      // Add more sample files here in the future
      // const recipeResult = await this.uploadRecipeSampleFile();
      // results.push({ type: "recipe_import_template", ...recipeResult });

      return {
        success: true,
        results,
      };

    } catch (error) {
      console.error("Error uploading sample files:", error);
      return {
        success: false,
        results: [],
        error: error,
      };
    }
  }

  /**
   * Get sample file URL by type
   */
  async getSampleFileUrl(fileType: string): Promise<string | null> {
    try {
      let fileName = "";

      switch (fileType) {
        case "ingredient_import_template":
          fileName = "ingredient_import_template.xlsx";
          break;
        default:
          return null;
      }

      const s3Path = RECIPE_FILE_UPLOAD_CONSTANT.SAMPLE_FILES.destinationPath(
        null,
        "ingredients",
        fileName
      );

      const item = await Item.findOne({
        where: {
          item_location: s3Path,
          item_organization_id: null as any,
        },
      });

      if (item) {
        return this.generateFileUrl(item.item_location);
      }

      return null;
    } catch (error) {
      console.error("Error getting sample file URL:", error);
      return null;
    }
  }
}

export default new SampleFileUploadService();
