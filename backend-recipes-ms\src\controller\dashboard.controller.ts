import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import analyticsService from "../services/analytics.service";
import { sequelize } from "../models/index";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../helper/transaction.helper";
import { Valida<PERSON><PERSON>elper } from "../helper/validation.helper";

/**
 * Get dashboard overview statistics - ROBUST VERSION
 * @route GET /api/v1/private/dashboard/overview
 * @access Private (Authenticated users)
 */
const getDashboardOverview = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { date_range = "last_30_days" } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    // Validate date_range parameter
    const validDateRanges = ["last_7_days", "last_30_days", "last_90_days", "custom"];
    if (!validDateRanges.includes(date_range)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Invalid date_range. Must be one of: " + validDateRanges.join(", ")
      });
    }

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Analytics service timeout")), 10000); // 10 second timeout
    });

    // Get dashboard stats from analytics service with timeout
    const stats: any = await Promise.race([
      analyticsService.getDashboardStats(effectiveOrganizationId || undefined, date_range as string),
      timeoutPromise,
    ]);

    const overviewData = {
      // Core Statistics - Use analytics service data
      totalRecipes: stats.totalRecipes,
      totalIngredients: stats.totalIngredients,
      totalCategories: stats.totalCategories,
      totalFoodAttributes: stats.totalFoodAttributes,
      totalRecipeMeasures: stats.totalRecipeMeasures,
      totalPublicRecipes: stats.totalPublicRecipes,

      // Analytics Statistics
      totalViews: stats.totalViews,
      totalCtaClicks: stats.totalCtaClicks,
      totalContactSubmissions: stats.totalContactSubmissions,

      // Top Performers
      topViewedRecipes: stats.topViewedRecipes,
      topClickedRecipes: stats.topClickedRecipes,

      // Recent Activity
      recentActivity: stats.recentActivity,

      // Quick Actions
      // quickActions: [
      //   {
      //     title: "Create New Recipe",
      //     description: "Start creating a new recipe",
      //     icon: "plus",
      //     action: "/recipes/create",
      //     color: "primary",
      //   },
      //   {
      //     title: "Manage Ingredients",
      //     description: "Add or edit ingredients",
      //     icon: "ingredients",
      //     action: "/ingredients",
      //     color: "success",
      //   },
      //   {
      //     title: "View Categories",
      //     description: "Manage recipe categories",
      //     icon: "category",
      //     action: "/categories",
      //     color: "info",
      //   },
      //   {
      //     title: "Settings",
      //     description: "Configure system settings",
      //     icon: "settings",
      //     action: "/settings",
      //     color: "warning",
      //   },
      // ],
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Dashboard overview fetched successfully",
      data: overviewData,
      timestamp: new Date().toISOString(),
    });
  } catch (error: any) {
    console.error("Dashboard overview error:", error);

    // Return safe fallback data instead of error
    const fallbackData = {
      totalRecipes: 0,
      totalIngredients: 0,
      totalCategories: 0,
      totalFoodAttributes: 0,
      totalRecipeMeasures: 0,
      totalPublicRecipes: 0,
      totalViews: 0,
      totalCtaClicks: 0,
      totalContactSubmissions: 0,
      topViewedRecipes: [],
      topClickedRecipes: [],
      recentActivity: [],
      quickActions: [
        {
          title: "Create New Recipe",
          description: "Start creating a new recipe",
          icon: "plus",
          action: "/recipes/create",
          color: "primary",
        },
        {
          title: "Manage Ingredients",
          description: "Add or edit ingredients",
          icon: "ingredients",
          action: "/ingredients",
          color: "success",
        },
        {
          title: "View Categories",
          description: "Manage recipe categories",
          icon: "category",
          action: "/categories",
          color: "info",
        },
        {
          title: "Settings",
          description: "Configure system settings",
          icon: "settings",
          action: "/settings",
          color: "warning",
        },
      ],
    };

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Dashboard overview fetched (fallback data)",
      data: fallbackData,
      timestamp: new Date().toISOString(),
      warning: "Some data may be incomplete due to system issues",
    });
  }
};

/**
 * Get public recipe analytics
 * @route GET /api/v1/private/dashboard/public-analytics
 * @access Private (Authenticated users)
 */
const getPublicAnalytics = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { date_range = "last_30_days" } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    const [ctaAnalytics, contactAnalytics] = await Promise.all([
      analyticsService.getCtaClickAnalytics(
        effectiveOrganizationId || undefined,
        date_range as string
      ),
      analyticsService.getContactSubmissionAnalytics(
        effectiveOrganizationId || undefined,
        date_range as string
      ),
    ]);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: {
        ctaClicks: ctaAnalytics,
        contactSubmissions: contactAnalytics,
        summary: {
          totalCtaClicks: ctaAnalytics.reduce(
            (sum: number, item: any) => sum + item.clicks,
            0
          ),
          totalContactSubmissions: contactAnalytics.length,
          dateRange: date_range,
        },
      },
    });
  } catch (error: any) {
    return ErrorHandler.createErrorResponse(error, res, "Error fetching public analytics");
  }
};

/**
 * Get system health status
 * @route GET /api/v1/private/dashboard/health
 */
const getSystemHealth = async (req: Request, res: Response): Promise<any> => {
  try {
    const healthChecks = {
      database: false,
      storage: false,
      analytics: false,
      overall: false,
    };

    // Check database connection
    try {
      await sequelize.authenticate();
      healthChecks.database = true;
    } catch (error) {
      console.error("Database health check failed:", error);
    }

    // Check storage (basic check)
    try {
      // This would check your file storage system
      healthChecks.storage = true;
    } catch (error) {
      console.error("Storage health check failed:", error);
    }

    // Check analytics
    try {
      await analyticsService.getAnalytics({ limit: 1 });
      healthChecks.analytics = true;
    } catch (error) {
      console.error("Analytics health check failed:", error);
    }

    // Overall health
    healthChecks.overall =
      healthChecks.database && healthChecks.storage && healthChecks.analytics;

    const status = healthChecks.overall
      ? StatusCodes.OK
      : StatusCodes.SERVICE_UNAVAILABLE;

    return res.status(status).json({
      status: healthChecks.overall,
      message: healthChecks.overall ? "System is healthy" : "System has issues",
      data: {
        checks: healthChecks,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Health check failed",
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

/**
 * Export dashboard data
 * @route GET /api/v1/private/dashboard/export
 */
const exportDashboardData = async (req: any, res: Response): Promise<any> => {
  try {
    // Input validation and sanitization
    const sanitizedQuery = ValidationHelper.sanitizeInput(req.query);
    const { format = "json", date_range = "last_30_days" } = sanitizedQuery;

    // Get effective organization ID (handles admin users)
    const effectiveOrganizationId = await ValidationHelper.getEffectiveOrganizationId(
      req.user,
      sanitizedQuery.organization_id
    );

    const dashboardData = await analyticsService.getDashboardStats(
      effectiveOrganizationId || undefined,
      date_range as string
    );

    if (format === "csv") {
      // Convert to CSV format
      const csv = convertToCSV(dashboardData);
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.csv`
      );
      return res.send(csv);
    }

    // Default JSON export
    res.setHeader("Content-Type", "application/json");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=dashboard-export-${new Date().toISOString().split("T")[0]}.json`
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("DASHBOARD_DATA_EXPORTED_SUCCESSFULLY"),
      data: dashboardData,
      exportedAt: new Date().toISOString(),
    });
  } catch (error: any) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_EXPORTING_DASHBOARD_DATA"),
      error: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
};

// Helper function to convert data to CSV
function convertToCSV(data: any): string {
  const headers = ["Metric", "Value"];
  const rows = [
    ["Total Recipes", data.totalRecipes],
    ["Total Ingredients", data.totalIngredients],
    ["Total Categories", data.totalCategories],
    ["Total Public Recipes", data.totalPublicRecipes],
    ["Total Views", data.totalViews],
    ["Total CTA Clicks", data.totalCtaClicks],
    ["Total Contact Submissions", data.totalContactSubmissions],
  ];

  const csvContent = [
    headers.join(","),
    ...rows.map((row) => row.join(",")),
  ].join("\n");

  return csvContent;
}

export default {
  getDashboardOverview,
  getPublicAnalytics,
  getSystemHealth,
  exportDashboardData,
};
