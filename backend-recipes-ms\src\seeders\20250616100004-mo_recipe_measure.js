const { QueryTypes } = require("sequelize");

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    // Check if measurement units already exist
    const existingUnits = await queryInterface.sequelize.query(
      `SELECT * FROM mo_recipe_measure WHERE organization_id IS NULL AND is_system_unit = true`,
      { type: QueryTypes.SELECT }
    );

    if (existingUnits.length === 0) {
      // Measurement Units
      const measurementUnits = [
        // Weight Units
        { name: "Gram", slug: "gram" },
        { name: "Kilogram", slug: "kilogram" },
        { name: "Ounce", slug: "ounce" },
        { name: "Pound", slug: "pound" },

        // Volume Units
        { name: "Milliliter", slug: "milliliter" },
        { name: "Liter", slug: "liter" },
        { name: "Tablespoon", slug: "tablespoon" },
        { name: "Teaspoon", slug: "teaspoon" },
        { name: "Cup", slug: "cup" },
        { name: "Pint", slug: "pint" },
        { name: "Quart", slug: "quart" },
        { name: "<PERSON>allon", slug: "gallon" },
        { name: "Fluid Ounce", slug: "fluid-ounce" },

        // Count Units
        { name: "Unit", slug: "unit" },
        { name: "Piece", slug: "piece" },
        { name: "Item", slug: "item" },
        { name: "Each", slug: "each" },
        { name: "Dozen", slug: "dozen" },

        // Cooking Measurements
        { name: "Pinch", slug: "pinch" },
        { name: "Dash", slug: "dash" },
        { name: "Drop", slug: "drop" },
        { name: "Splash", slug: "splash" },
        { name: "Handful", slug: "handful" },

        // Metric Cooking
        { name: "Deciliter", slug: "deciliter" },
        { name: "Centiliter", slug: "centiliter" },

        // Imperial Cooking
        { name: "Gill", slug: "gill" },
        { name: "Dram", slug: "dram" },

        // Specialty Units
        { name: "Clove", slug: "clove" },
        { name: "Bulb", slug: "bulb" },
        { name: "Head", slug: "head" },
        { name: "Bunch", slug: "bunch" },
        { name: "Sprig", slug: "sprig" },
        { name: "Leaf", slug: "leaf" },
        { name: "Slice", slug: "slice" },
        { name: "Stick", slug: "stick" },
        { name: "Can", slug: "can" },
        { name: "Jar", slug: "jar" },
        { name: "Bottle", slug: "bottle" },
        { name: "Package", slug: "package" },
        { name: "Box", slug: "box" },
        { name: "Bag", slug: "bag" }
      ];

      // Prepare bulk insert data
      const unitData = measurementUnits.map(unit => ({
        unit_title: unit.name,
        unit_slug: unit.slug,
        unit_icon: null,
        status: "active",
        organization_id: null,
        is_system_unit: true,
        created_by: 1,
        updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }));

      await queryInterface.bulkInsert("mo_recipe_measure", unitData);
      console.log("✅ Recipe Measurement Units seeded successfully");
    } else {
      console.log("⏭️  Recipe Measurement Units already exist, skipping...");
    }
  },

  async down(queryInterface) {
    await queryInterface.bulkDelete("mo_recipe_measure", {
      organization_id: null,
      is_system_unit: true
    });
  }
};
