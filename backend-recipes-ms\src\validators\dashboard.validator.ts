import { celebrate, Joi, Segments } from "celebrate";

/**
 * Validator for dashboard overview endpoint
 * Validates date_range query parameter
 */
const getDashboardOverviewValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .default("last_30_days")
        .description("Date range for dashboard analytics"),
    },
  });

/**
 * Validator for public analytics endpoint
 * Validates date_range query parameter
 */
const getPublicAnalyticsValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .default("last_30_days")
        .description("Date range for public analytics"),
    },
  });

/**
 * Validator for dashboard export endpoint
 * Validates format and date_range query parameters
 */
const exportDashboardDataValidator = () =>
  celebrate({
    [Segments.QUERY]: {
      format: Joi.string()
        .valid("json", "csv")
        .default("json")
        .description("Export format"),
      date_range: Joi.string()
        .valid("last_7_days", "last_30_days", "last_90_days", "last_year")
        .default("last_30_days")
        .description("Date range for exported data"),
    },
  });

export default {
  getDashboardOverviewValidator,
  getPublicAnalyticsValidator,
  exportDashboardDataValidator,
};
