import { celebrate, Joi, Segments } from "celebrate";

const createContactUsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      name: Joi.string().max(100).required(),
      email: Joi.string().email().max(255).required(),
      mobile: Joi.alternatives()
        .try(Joi.string().max(20), Joi.string().allow(null, ""))
        .optional(),
      message: Joi.string().max(1000).required(),
      subject: Joi.string().max(200).optional(),
      recipe_id: Joi.alternatives()
        .try(
          Joi.number().integer().min(1),
          Joi.string()
            .pattern(/^\d+$/)
            .custom((value) => parseInt(value))
        )
        .allow(null)
        .optional(),
    }),
  });

const updateContactUsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      name: Joi.string().max(100).optional(),
      email: Joi.string().email().max(255).optional(),
      mobile: Joi.string().max(20).allow(null, "").optional(),
      message: Joi.string().optional(),
      recipe_id: Joi.number().integer().allow(null).optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const deleteContactUsValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getContactUsValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getContactUsListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(10),
      search: Joi.string().max(100).optional(),
      start_date: Joi.date().iso().optional(),
      end_date: Joi.date().iso().optional(),
      sort_by: Joi.string()
        .valid("name", "email", "created_at", "updated_at")
        .default("created_at"),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
    }),
  });

export default {
  createContactUsValidator,
  updateContactUsValidator,
  deleteContactUsValidator,
  getContactUsValidator,
  getContactUsListValidator,
};
